# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
PYTHONPATH

# Virtual Environment
venv/
env/
ENV/
env.bak/
venv.bak/
.venv_deps_installed

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Temporary files
temp/
tmp/
*.tmp

# Node.js (for frontend)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# API Keys and sensitive data
data/settings.json

# Upload directories (may contain large files)
uploads/*/
!uploads/.gitkeep
!uploads/tmp/

# Temporary download files
uploads/tmp/*.zip

# Test files
test_*.json
test_*.html

# Assets directory (keep for QR codes)
!assets/
!assets/*.png
!assets/*.jpg
!assets/*.jpeg

# GitHub configuration directory
!.github/
!.github/*.png
!.github/*.jpg
!.github/*.jpeg

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/
.pytest_cache/