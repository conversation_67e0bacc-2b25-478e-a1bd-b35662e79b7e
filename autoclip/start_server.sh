#!/bin/bash

# AutoClip 服务器启动脚本
# 用于在生产环境中启动 FastAPI 后端服务

cd /www/wwwroot/su.guiyunai.fun/autoclip

# 激活虚拟环境
source venv/bin/activate

# 设置环境变量
export PYTHONPATH=/www/wwwroot/su.guiyunai.fun/autoclip:$PYTHONPATH

# 创建必要的目录
mkdir -p uploads output clips collections metadata

# 启动服务器
echo "启动 AutoClip 服务器..."
echo "访问地址: http://su.guiyunai.fun:8000"
echo "按 Ctrl+C 停止服务器"

# 使用 uvicorn 启动服务器，绑定到所有接口
uvicorn backend_server:app --host 0.0.0.0 --port 8000 --reload
